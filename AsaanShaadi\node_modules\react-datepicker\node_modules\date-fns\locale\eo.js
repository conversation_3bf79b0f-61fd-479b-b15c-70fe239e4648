import { formatDistance } from "./eo/_lib/formatDistance.js";
import { formatLong } from "./eo/_lib/formatLong.js";
import { formatRelative } from "./eo/_lib/formatRelative.js";
import { localize } from "./eo/_lib/localize.js";
import { match } from "./eo/_lib/match.js";

/**
 * @category Locales
 * @summary Esperanto locale.
 * @language Esperanto
 * @iso-639-2 epo
 * <AUTHOR>
 */
export const eo = {
  code: "eo",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default eo;
