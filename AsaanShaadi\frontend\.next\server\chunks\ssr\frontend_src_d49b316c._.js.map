{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Shaadi%20app/AsaanShaadi/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Shaadi%20app/AsaanShaadi/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Shaadi%20app/AsaanShaadi/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Shaadi%20app/AsaanShaadi/frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { useAuthStore } from \"@/store/authStore\";\nimport { Loader2 } from \"lucide-react\";\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredRole?: string[];\n  redirectTo?: string;\n}\n\nexport function ProtectedRoute({ \n  children, \n  requiredRole = [], \n  redirectTo = \"/auth/login\" \n}: ProtectedRouteProps) {\n  const router = useRouter();\n  const { user, isAuthenticated, isLoading } = useAuthStore();\n\n  useEffect(() => {\n    // Wait for auth state to be loaded from localStorage\n    if (isLoading) return;\n\n    // If not authenticated, redirect to login\n    if (!isAuthenticated || !user) {\n      router.push(redirectTo);\n      return;\n    }\n\n    // If specific roles are required, check user role\n    if (requiredRole.length > 0 && !requiredRole.includes(user.role)) {\n      // Redirect based on user role\n      if (user.role === 'ADMIN') {\n        router.push('/admin');\n      } else if (user.role === 'VENDOR') {\n        router.push('/vendor');\n      } else {\n        router.push('/');\n      }\n      return;\n    }\n  }, [isAuthenticated, user, isLoading, router, requiredRole, redirectTo]);\n\n  // Show loading while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Loader2 className=\"h-8 w-8 animate-spin mx-auto mb-4\" />\n          <p className=\"text-muted-foreground\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Don't render children if not authenticated or wrong role\n  if (!isAuthenticated || !user) {\n    return null;\n  }\n\n  if (requiredRole.length > 0 && !requiredRole.includes(user.role)) {\n    return null;\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAaO,SAAS,eAAe,EAC7B,QAAQ,EACR,eAAe,EAAE,EACjB,aAAa,aAAa,EACN;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;IAExD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qDAAqD;QACrD,IAAI,WAAW;QAEf,0CAA0C;QAC1C,IAAI,CAAC,mBAAmB,CAAC,MAAM;YAC7B,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,kDAAkD;QAClD,IAAI,aAAa,MAAM,GAAG,KAAK,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YAChE,8BAA8B;YAC9B,IAAI,KAAK,IAAI,KAAK,SAAS;gBACzB,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,KAAK,IAAI,KAAK,UAAU;gBACjC,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;YACA;QACF;IACF,GAAG;QAAC;QAAiB;QAAM;QAAW;QAAQ;QAAc;KAAW;IAEvE,6CAA6C;IAC7C,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,2DAA2D;IAC3D,IAAI,CAAC,mBAAmB,CAAC,MAAM;QAC7B,OAAO;IACT;IAEA,IAAI,aAAa,MAAM,GAAG,KAAK,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QAChE,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Shaadi%20app/AsaanShaadi/frontend/src/app/caterer/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Tabs, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from \"@/components/ui/tabs\";\nimport { ProtectedRoute } from \"@/components/auth/ProtectedRoute\";\nimport { useAuthStore } from \"@/store/authStore\";\nimport { \n  ChefHat, \n  Users, \n  Calendar, \n  DollarSign, \n  TrendingUp,\n  Eye,\n  Edit,\n  Plus,\n  Star,\n  Clock,\n  Package\n} from \"lucide-react\";\n\n// Mock data for caterer dashboard\nconst catererStats = {\n  totalServices: 2,\n  totalOrders: 12,\n  monthlyRevenue: 180000,\n  pendingOrders: 3,\n  averageRating: 4.6,\n  totalReviews: 67,\n};\n\nconst recentOrders = [\n  {\n    id: \"1\",\n    customerName: \"<PERSON> Khan\",\n    serviceName: \"Royal Catering Services\",\n    eventDate: \"2024-08-15\",\n    status: \"confirmed\",\n    amount: 75000,\n    guestCount: 500,\n    menuType: \"Pakistani Deluxe\",\n  },\n  {\n    id: \"2\",\n    customerName: \"Fatima Ali\",\n    serviceName: \"Spice Garden Catering\",\n    eventDate: \"2024-07-20\",\n    status: \"pending\",\n    amount: 36000,\n    guestCount: 300,\n    menuType: \"Continental\",\n  },\n  {\n    id: \"3\",\n    customerName: \"Hassan Sheikh\",\n    serviceName: \"Royal Catering Services\",\n    eventDate: \"2024-09-10\",\n    status: \"confirmed\",\n    amount: 90000,\n    guestCount: 450,\n    menuType: \"Mixed Cuisine\",\n  },\n];\n\nconst myServices = [\n  {\n    id: \"1\",\n    name: \"Royal Catering Services\",\n    pricePerPerson: 1500,\n    minimumOrder: 100,\n    isActive: true,\n    rating: 4.7,\n    reviewCount: 89,\n    ordersThisMonth: 8,\n    cuisines: [\"Pakistani\", \"Continental\", \"Chinese\"],\n  },\n  {\n    id: \"2\",\n    name: \"Spice Garden Catering\",\n    pricePerPerson: 1200,\n    minimumOrder: 50,\n    isActive: true,\n    rating: 4.5,\n    reviewCount: 156,\n    ordersThisMonth: 4,\n    cuisines: [\"Pakistani\", \"Indian\", \"Arabic\"],\n  },\n];\n\nexport default function CatererDashboard() {\n  const { user } = useAuthStore();\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case \"confirmed\":\n        return \"bg-green-100 text-green-800\";\n      case \"pending\":\n        return \"bg-yellow-100 text-yellow-800\";\n      case \"cancelled\":\n        return \"bg-red-100 text-red-800\";\n      default:\n        return \"bg-gray-100 text-gray-800\";\n    }\n  };\n\n  return (\n    <ProtectedRoute requiredRole={['VENDOR', 'ADMIN']}>\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl lg:text-4xl font-bold mb-4\">\n            Welcome back, {user?.firstName}!\n          </h1>\n          <p className=\"text-xl text-muted-foreground\">\n            Manage your catering services and track your orders\n          </p>\n        </div>\n\n        {/* Stats Overview */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">My Services</CardTitle>\n              <ChefHat className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{catererStats.totalServices}</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Active services\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Orders</CardTitle>\n              <Calendar className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{catererStats.totalOrders}</div>\n              <p className=\"text-xs text-muted-foreground\">\n                This month\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Revenue</CardTitle>\n              <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">₹{(catererStats.monthlyRevenue / 1000).toFixed(0)}K</div>\n              <p className=\"text-xs text-muted-foreground\">\n                This month\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Pending</CardTitle>\n              <Clock className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{catererStats.pendingOrders}</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Awaiting response\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Rating</CardTitle>\n              <Star className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{catererStats.averageRating}</div>\n              <p className=\"text-xs text-muted-foreground\">\n                {catererStats.totalReviews} reviews\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Growth</CardTitle>\n              <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">+18%</div>\n              <p className=\"text-xs text-muted-foreground\">\n                vs last month\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n\n        <Tabs defaultValue=\"orders\" className=\"space-y-6\">\n          <TabsList className=\"grid w-full grid-cols-4\">\n            <TabsTrigger value=\"orders\">Recent Orders</TabsTrigger>\n            <TabsTrigger value=\"services\">My Services</TabsTrigger>\n            <TabsTrigger value=\"menu\">Menu Management</TabsTrigger>\n            <TabsTrigger value=\"analytics\">Analytics</TabsTrigger>\n          </TabsList>\n\n          {/* Recent Orders */}\n          <TabsContent value=\"orders\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Recent Orders</CardTitle>\n                <CardDescription>\n                  Latest catering orders for your services\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {recentOrders.map((order) => (\n                    <div key={order.id} className=\"flex items-center justify-between p-4 border rounded-lg\">\n                      <div className=\"space-y-1\">\n                        <p className=\"font-medium\">{order.customerName}</p>\n                        <p className=\"text-sm text-muted-foreground\">{order.serviceName}</p>\n                        <p className=\"text-sm text-muted-foreground\">\n                          {new Date(order.eventDate).toLocaleDateString()} • {order.guestCount} guests • {order.menuType}\n                        </p>\n                      </div>\n                      <div className=\"flex items-center gap-4\">\n                        <div className=\"text-right\">\n                          <p className=\"font-medium\">₹{order.amount.toLocaleString()}</p>\n                          <Badge className={getStatusColor(order.status)}>\n                            {order.status}\n                          </Badge>\n                        </div>\n                        <div className=\"flex gap-2\">\n                          <Button size=\"sm\" variant=\"outline\">\n                            <Eye className=\"h-4 w-4\" />\n                          </Button>\n                          {order.status === 'pending' && (\n                            <>\n                              <Button size=\"sm\">Accept</Button>\n                              <Button size=\"sm\" variant=\"destructive\">Decline</Button>\n                            </>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          {/* My Services */}\n          <TabsContent value=\"services\">\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between\">\n                <div>\n                  <CardTitle>My Catering Services</CardTitle>\n                  <CardDescription>\n                    Manage your catering service listings\n                  </CardDescription>\n                </div>\n                <Button>\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Add Service\n                </Button>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {myServices.map((service) => (\n                    <div key={service.id} className=\"flex items-center justify-between p-4 border rounded-lg\">\n                      <div className=\"space-y-1\">\n                        <div className=\"flex items-center gap-2\">\n                          <p className=\"font-medium\">{service.name}</p>\n                          <Badge variant={service.isActive ? \"default\" : \"secondary\"}>\n                            {service.isActive ? \"Active\" : \"Inactive\"}\n                          </Badge>\n                        </div>\n                        <p className=\"text-sm text-muted-foreground\">\n                          ₹{service.pricePerPerson}/person • Min {service.minimumOrder} guests\n                        </p>\n                        <div className=\"flex items-center gap-4 text-sm text-muted-foreground\">\n                          <span className=\"flex items-center gap-1\">\n                            <Star className=\"h-3 w-3 fill-yellow-400 text-yellow-400\" />\n                            {service.rating} ({service.reviewCount} reviews)\n                          </span>\n                          <span>{service.ordersThisMonth} orders this month</span>\n                        </div>\n                        <div className=\"flex gap-1\">\n                          {service.cuisines.map((cuisine) => (\n                            <Badge key={cuisine} variant=\"outline\" className=\"text-xs\">\n                              {cuisine}\n                            </Badge>\n                          ))}\n                        </div>\n                      </div>\n                      <div className=\"flex gap-2\">\n                        <Button size=\"sm\" variant=\"outline\">\n                          <Eye className=\"h-4 w-4\" />\n                        </Button>\n                        <Button size=\"sm\" variant=\"outline\">\n                          <Edit className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          {/* Menu Management */}\n          <TabsContent value=\"menu\">\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between\">\n                <div>\n                  <CardTitle>Menu Management</CardTitle>\n                  <CardDescription>\n                    Manage your menu items and packages\n                  </CardDescription>\n                </div>\n                <Button>\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Add Menu Item\n                </Button>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-center py-12\">\n                  <Package className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-semibold mb-2\">Menu Management</h3>\n                  <p className=\"text-muted-foreground\">\n                    Create and manage your menu items, packages, and pricing\n                  </p>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          {/* Analytics */}\n          <TabsContent value=\"analytics\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Analytics & Insights</CardTitle>\n                <CardDescription>\n                  Performance metrics for your catering services\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-center py-12\">\n                  <TrendingUp className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-semibold mb-2\">Analytics Dashboard</h3>\n                  <p className=\"text-muted-foreground\">\n                    Detailed analytics and insights coming soon\n                  </p>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n        </Tabs>\n      </div>\n    </ProtectedRoute>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;AAuBA,kCAAkC;AAClC,MAAM,eAAe;IACnB,eAAe;IACf,aAAa;IACb,gBAAgB;IAChB,eAAe;IACf,eAAe;IACf,cAAc;AAChB;AAEA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,WAAW;QACX,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,WAAW;QACX,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,WAAW;QACX,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,UAAU;IACZ;CACD;AAED,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,MAAM;QACN,gBAAgB;QAChB,cAAc;QACd,UAAU;QACV,QAAQ;QACR,aAAa;QACb,iBAAiB;QACjB,UAAU;YAAC;YAAa;YAAe;SAAU;IACnD;IACA;QACE,IAAI;QACJ,MAAM;QACN,gBAAgB;QAChB,cAAc;QACd,UAAU;QACV,QAAQ;QACR,aAAa;QACb,iBAAiB;QACjB,UAAU;YAAC;YAAa;YAAU;SAAS;IAC7C;CACD;AAEc,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,wJAAA,CAAA,iBAAc;QAAC,cAAc;YAAC;YAAU;SAAQ;kBAC/C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAsC;gCACnC,MAAM;gCAAU;;;;;;;sCAEjC,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;8BAM/C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4IAAA,CAAA,OAAI;;8CACH,8OAAC,4IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,4IAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;8CAErB,8OAAC,4IAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAsB,aAAa,aAAa;;;;;;sDAC/D,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC,4IAAA,CAAA,OAAI;;8CACH,8OAAC,4IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,4IAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,8OAAC,4IAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAsB,aAAa,WAAW;;;;;;sDAC7D,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC,4IAAA,CAAA,OAAI;;8CACH,8OAAC,4IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,4IAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,8OAAC,4IAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;;gDAAqB;gDAAE,CAAC,aAAa,cAAc,GAAG,IAAI,EAAE,OAAO,CAAC;gDAAG;;;;;;;sDACtF,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC,4IAAA,CAAA,OAAI;;8CACH,8OAAC,4IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,4IAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,8OAAC,4IAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAsB,aAAa,aAAa;;;;;;sDAC/D,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC,4IAAA,CAAA,OAAI;;8CACH,8OAAC,4IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,4IAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;8CAElB,8OAAC,4IAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAsB,aAAa,aAAa;;;;;;sDAC/D,8OAAC;4CAAE,WAAU;;gDACV,aAAa,YAAY;gDAAC;;;;;;;;;;;;;;;;;;;sCAKjC,8OAAC,4IAAA,CAAA,OAAI;;8CACH,8OAAC,4IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,4IAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,8OAAC,4IAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;8BAOnD,8OAAC,4IAAA,CAAA,OAAI;oBAAC,cAAa;oBAAS,WAAU;;sCACpC,8OAAC,4IAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,4IAAA,CAAA,cAAW;oCAAC,OAAM;8CAAS;;;;;;8CAC5B,8OAAC,4IAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,8OAAC,4IAAA,CAAA,cAAW;oCAAC,OAAM;8CAAO;;;;;;8CAC1B,8OAAC,4IAAA,CAAA,cAAW;oCAAC,OAAM;8CAAY;;;;;;;;;;;;sCAIjC,8OAAC,4IAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,8OAAC,4IAAA,CAAA,OAAI;;kDACH,8OAAC,4IAAA,CAAA,aAAU;;0DACT,8OAAC,4IAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,4IAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,4IAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;oDAAmB,WAAU;;sEAC5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAe,MAAM,YAAY;;;;;;8EAC9C,8OAAC;oEAAE,WAAU;8EAAiC,MAAM,WAAW;;;;;;8EAC/D,8OAAC;oEAAE,WAAU;;wEACV,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;wEAAG;wEAAI,MAAM,UAAU;wEAAC;wEAAW,MAAM,QAAQ;;;;;;;;;;;;;sEAGlG,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;;gFAAc;gFAAE,MAAM,MAAM,CAAC,cAAc;;;;;;;sFACxD,8OAAC,6IAAA,CAAA,QAAK;4EAAC,WAAW,eAAe,MAAM,MAAM;sFAC1C,MAAM,MAAM;;;;;;;;;;;;8EAGjB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,8IAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAQ;sFACxB,cAAA,8OAAC,gMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;;;;;;wEAEhB,MAAM,MAAM,KAAK,2BAChB;;8FACE,8OAAC,8IAAA,CAAA,SAAM;oFAAC,MAAK;8FAAK;;;;;;8FAClB,8OAAC,8IAAA,CAAA,SAAM;oFAAC,MAAK;oFAAK,SAAQ;8FAAc;;;;;;;;;;;;;;;;;;;;;mDAtBxC,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;sCAmC5B,8OAAC,4IAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,8OAAC,4IAAA,CAAA,OAAI;;kDACH,8OAAC,4IAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC;;kEACC,8OAAC,4IAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,4IAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,8OAAC,8IAAA,CAAA,SAAM;;kEACL,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAIrC,8OAAC,4IAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAC,wBACf,8OAAC;oDAAqB,WAAU;;sEAC9B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAe,QAAQ,IAAI;;;;;;sFACxC,8OAAC,6IAAA,CAAA,QAAK;4EAAC,SAAS,QAAQ,QAAQ,GAAG,YAAY;sFAC5C,QAAQ,QAAQ,GAAG,WAAW;;;;;;;;;;;;8EAGnC,8OAAC;oEAAE,WAAU;;wEAAgC;wEACzC,QAAQ,cAAc;wEAAC;wEAAe,QAAQ,YAAY;wEAAC;;;;;;;8EAE/D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;;8FACd,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;gFACf,QAAQ,MAAM;gFAAC;gFAAG,QAAQ,WAAW;gFAAC;;;;;;;sFAEzC,8OAAC;;gFAAM,QAAQ,eAAe;gFAAC;;;;;;;;;;;;;8EAEjC,8OAAC;oEAAI,WAAU;8EACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,8OAAC,6IAAA,CAAA,QAAK;4EAAe,SAAQ;4EAAU,WAAU;sFAC9C;2EADS;;;;;;;;;;;;;;;;sEAMlB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8IAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;8EACxB,cAAA,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;8EAEjB,8OAAC,8IAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;8EACxB,cAAA,8OAAC,2MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;;;;;;;;mDA/BZ,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;sCA0C9B,8OAAC,4IAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,8OAAC,4IAAA,CAAA,OAAI;;kDACH,8OAAC,4IAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC;;kEACC,8OAAC,4IAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,4IAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,8OAAC,8IAAA,CAAA,SAAM;;kEACL,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAIrC,8OAAC,4IAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS7C,8OAAC,4IAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,8OAAC,4IAAA,CAAA,OAAI;;kDACH,8OAAC,4IAAA,CAAA,aAAU;;0DACT,8OAAC,4IAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,4IAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,4IAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvD", "debugId": null}}]}