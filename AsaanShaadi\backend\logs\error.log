{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"asaan-shaadi-backend","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\Shaadi app\\AsaanShaadi\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Desktop\\Shaadi app\\AsaanShaadi\\backend\\dist\\index.js:75:13)","syscall":"listen","timestamp":"2025-06-27T15:41:15.984Z"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"asaan-shaadi-backend","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\Shaadi app\\AsaanShaadi\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Desktop\\Shaadi app\\AsaanShaadi\\backend\\dist\\index.js:75:13)","syscall":"listen","timestamp":"2025-06-27T15:47:57.709Z"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"asaan-shaadi-backend","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\Shaadi app\\AsaanShaadi\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Desktop\\Shaadi app\\AsaanShaadi\\backend\\dist\\index.js:75:13)","syscall":"listen","timestamp":"2025-06-27T15:48:25.270Z"}
