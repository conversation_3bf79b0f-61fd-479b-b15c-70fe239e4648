(()=>{var M;function j(C,G){var J=Object.keys(C);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(C);G&&(X=X.filter(function(U){return Object.getOwnPropertyDescriptor(C,U).enumerable})),J.push.apply(J,X)}return J}function R(C){for(var G=1;G<arguments.length;G++){var J=arguments[G]!=null?arguments[G]:{};G%2?j(Object(J),!0).forEach(function(X){v(C,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(C,Object.getOwnPropertyDescriptors(J)):j(Object(J)).forEach(function(X){Object.defineProperty(C,X,Object.getOwnPropertyDescriptor(J,X))})}return C}function v(C,G,J){if(G=F(G),G in C)Object.defineProperty(C,G,{value:J,enumerable:!0,configurable:!0,writable:!0});else C[G]=J;return C}function F(C){var G=b(C,"string");return K(G)=="symbol"?G:String(G)}function b(C,G){if(K(C)!="object"||!C)return C;var J=C[Symbol.toPrimitive];if(J!==void 0){var X=J.call(C,G||"default");if(K(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(C)}function h(C,G){return g(C)||k(C,G)||_(C,G)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _(C,G){if(!C)return;if(typeof C==="string")return W(C,G);var J=Object.prototype.toString.call(C).slice(8,-1);if(J==="Object"&&C.constructor)J=C.constructor.name;if(J==="Map"||J==="Set")return Array.from(C);if(J==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(J))return W(C,G)}function W(C,G){if(G==null||G>C.length)G=C.length;for(var J=0,X=new Array(G);J<G;J++)X[J]=C[J];return X}function k(C,G){var J=C==null?null:typeof Symbol!="undefined"&&C[Symbol.iterator]||C["@@iterator"];if(J!=null){var X,U,Z,B,H=[],q=!0,Y=!1;try{if(Z=(J=J.call(C)).next,G===0){if(Object(J)!==J)return;q=!1}else for(;!(q=(X=Z.call(J)).done)&&(H.push(X.value),H.length!==G);q=!0);}catch(E){Y=!0,U=E}finally{try{if(!q&&J.return!=null&&(B=J.return(),Object(B)!==B))return}finally{if(Y)throw U}}return H}}function g(C){if(Array.isArray(C))return C}function K(C){return K=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},K(C)}var y=Object.defineProperty,bC=function C(G,J){for(var X in J)y(G,X,{get:J[X],enumerable:!0,configurable:!0,set:function U(Z){return J[X]=function(){return Z}}})};function V(C,G){if(C.one!==void 0&&G===1)return C.one;var J=G%10,X=G%100;if(J===1&&X!==11)return C.singularNominative.replace("{{count}}",String(G));else if(J>=2&&J<=4&&(X<10||X>20))return C.singularGenitive.replace("{{count}}",String(G));else return C.pluralGenitive.replace("{{count}}",String(G))}function Q(C){return function(G,J){if(J&&J.addSuffix)if(J.comparison&&J.comparison>0)if(C.future)return V(C.future,G);else return"\u0437\u0430 "+V(C.regular,G);else if(C.past)return V(C.past,G);else return V(C.regular,G)+" \u0442\u043E\u043C\u0443";else return V(C.regular,G)}}var m=function C(G,J){if(J&&J.addSuffix)if(J.comparison&&J.comparison>0)return"\u0437\u0430 \u043F\u0456\u0432\u0445\u0432\u0438\u043B\u0438\u043D\u0438";else return"\u043F\u0456\u0432\u0445\u0432\u0438\u043B\u0438\u043D\u0438 \u0442\u043E\u043C\u0443";return"\u043F\u0456\u0432\u0445\u0432\u0438\u043B\u0438\u043D\u0438"},c={lessThanXSeconds:Q({regular:{one:"\u043C\u0435\u043D\u0448\u0435 \u0441\u0435\u043A\u0443\u043D\u0434\u0438",singularNominative:"\u043C\u0435\u043D\u0448\u0435 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438",singularGenitive:"\u043C\u0435\u043D\u0448\u0435 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434",pluralGenitive:"\u043C\u0435\u043D\u0448\u0435 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"},future:{one:"\u043C\u0435\u043D\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularNominative:"\u043C\u0435\u043D\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularGenitive:"\u043C\u0435\u043D\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438",pluralGenitive:"\u043C\u0435\u043D\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"}}),xSeconds:Q({regular:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0430",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434"},past:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443 \u0442\u043E\u043C\u0443",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438 \u0442\u043E\u043C\u0443",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434 \u0442\u043E\u043C\u0443"},future:{singularNominative:"\u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularGenitive:"\u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438",pluralGenitive:"\u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"}}),halfAMinute:m,lessThanXMinutes:Q({regular:{one:"\u043C\u0435\u043D\u0448\u0435 \u0445\u0432\u0438\u043B\u0438\u043D\u0438",singularNominative:"\u043C\u0435\u043D\u0448\u0435 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0438",singularGenitive:"\u043C\u0435\u043D\u0448\u0435 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D",pluralGenitive:"\u043C\u0435\u043D\u0448\u0435 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D"},future:{one:"\u043C\u0435\u043D\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 \u0445\u0432\u0438\u043B\u0438\u043D\u0443",singularNominative:"\u043C\u0435\u043D\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0443",singularGenitive:"\u043C\u0435\u043D\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0438",pluralGenitive:"\u043C\u0435\u043D\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D"}}),xMinutes:Q({regular:{singularNominative:"{{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0430",singularGenitive:"{{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0438",pluralGenitive:"{{count}} \u0445\u0432\u0438\u043B\u0438\u043D"},past:{singularNominative:"{{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0443 \u0442\u043E\u043C\u0443",singularGenitive:"{{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0438 \u0442\u043E\u043C\u0443",pluralGenitive:"{{count}} \u0445\u0432\u0438\u043B\u0438\u043D \u0442\u043E\u043C\u0443"},future:{singularNominative:"\u0437\u0430 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0443",singularGenitive:"\u0437\u0430 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D\u0438",pluralGenitive:"\u0437\u0430 {{count}} \u0445\u0432\u0438\u043B\u0438\u043D"}}),aboutXHours:Q({regular:{singularNominative:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0433\u043E\u0434\u0438\u043D\u0438",singularGenitive:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0433\u043E\u0434\u0438\u043D",pluralGenitive:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0433\u043E\u0434\u0438\u043D"},future:{singularNominative:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0433\u043E\u0434\u0438\u043D\u0443",singularGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0433\u043E\u0434\u0438\u043D\u0438",pluralGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0433\u043E\u0434\u0438\u043D"}}),xHours:Q({regular:{singularNominative:"{{count}} \u0433\u043E\u0434\u0438\u043D\u0443",singularGenitive:"{{count}} \u0433\u043E\u0434\u0438\u043D\u0438",pluralGenitive:"{{count}} \u0433\u043E\u0434\u0438\u043D"}}),xDays:Q({regular:{singularNominative:"{{count}} \u0434\u0435\u043D\u044C",singularGenitive:"{{count}} \u0434\u043Di",pluralGenitive:"{{count}} \u0434\u043D\u0456\u0432"}}),aboutXWeeks:Q({regular:{singularNominative:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0442\u0438\u0436\u043D\u044F",singularGenitive:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0442\u0438\u0436\u043D\u0456\u0432",pluralGenitive:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0442\u0438\u0436\u043D\u0456\u0432"},future:{singularNominative:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0442\u0438\u0436\u0434\u0435\u043D\u044C",singularGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0442\u0438\u0436\u043D\u0456",pluralGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0442\u0438\u0436\u043D\u0456\u0432"}}),xWeeks:Q({regular:{singularNominative:"{{count}} \u0442\u0438\u0436\u0434\u0435\u043D\u044C",singularGenitive:"{{count}} \u0442\u0438\u0436\u043D\u0456",pluralGenitive:"{{count}} \u0442\u0438\u0436\u043D\u0456\u0432"}}),aboutXMonths:Q({regular:{singularNominative:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u043C\u0456\u0441\u044F\u0446\u044F",singularGenitive:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u043C\u0456\u0441\u044F\u0446\u0456\u0432",pluralGenitive:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u043C\u0456\u0441\u044F\u0446\u0456\u0432"},future:{singularNominative:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u043C\u0456\u0441\u044F\u0446\u044C",singularGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u043C\u0456\u0441\u044F\u0446\u0456",pluralGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u043C\u0456\u0441\u044F\u0446\u0456\u0432"}}),xMonths:Q({regular:{singularNominative:"{{count}} \u043C\u0456\u0441\u044F\u0446\u044C",singularGenitive:"{{count}} \u043C\u0456\u0441\u044F\u0446\u0456",pluralGenitive:"{{count}} \u043C\u0456\u0441\u044F\u0446\u0456\u0432"}}),aboutXYears:Q({regular:{singularNominative:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0440\u043E\u043A\u0443",singularGenitive:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0440\u043E\u043A\u0456\u0432",pluralGenitive:"\u0431\u043B\u0438\u0437\u044C\u043A\u043E {{count}} \u0440\u043E\u043A\u0456\u0432"},future:{singularNominative:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0440\u0456\u043A",singularGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0440\u043E\u043A\u0438",pluralGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u043D\u043E \u0437\u0430 {{count}} \u0440\u043E\u043A\u0456\u0432"}}),xYears:Q({regular:{singularNominative:"{{count}} \u0440\u0456\u043A",singularGenitive:"{{count}} \u0440\u043E\u043A\u0438",pluralGenitive:"{{count}} \u0440\u043E\u043A\u0456\u0432"}}),overXYears:Q({regular:{singularNominative:"\u0431\u0456\u043B\u044C\u0448\u0435 {{count}} \u0440\u043E\u043A\u0443",singularGenitive:"\u0431\u0456\u043B\u044C\u0448\u0435 {{count}} \u0440\u043E\u043A\u0456\u0432",pluralGenitive:"\u0431\u0456\u043B\u044C\u0448\u0435 {{count}} \u0440\u043E\u043A\u0456\u0432"},future:{singularNominative:"\u0431\u0456\u043B\u044C\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0440\u0456\u043A",singularGenitive:"\u0431\u0456\u043B\u044C\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0440\u043E\u043A\u0438",pluralGenitive:"\u0431\u0456\u043B\u044C\u0448\u0435, \u043D\u0456\u0436 \u0437\u0430 {{count}} \u0440\u043E\u043A\u0456\u0432"}}),almostXYears:Q({regular:{singularNominative:"\u043C\u0430\u0439\u0436\u0435 {{count}} \u0440\u0456\u043A",singularGenitive:"\u043C\u0430\u0439\u0436\u0435 {{count}} \u0440\u043E\u043A\u0438",pluralGenitive:"\u043C\u0430\u0439\u0436\u0435 {{count}} \u0440\u043E\u043A\u0456\u0432"},future:{singularNominative:"\u043C\u0430\u0439\u0436\u0435 \u0437\u0430 {{count}} \u0440\u0456\u043A",singularGenitive:"\u043C\u0430\u0439\u0436\u0435 \u0437\u0430 {{count}} \u0440\u043E\u043A\u0438",pluralGenitive:"\u043C\u0430\u0439\u0436\u0435 \u0437\u0430 {{count}} \u0440\u043E\u043A\u0456\u0432"}})},p=function C(G,J,X){return X=X||{},c[G](J,X)};function x(C){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=G.width?String(G.width):C.defaultWidth,X=C.formats[J]||C.formats[C.defaultWidth];return X}}var u={full:"EEEE, do MMMM y '\u0440.'",long:"do MMMM y '\u0440.'",medium:"d MMM y '\u0440.'",short:"dd.MM.y"},d={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},l={full:"{{date}} '\u043E' {{time}}",long:"{{date}} '\u043E' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},i={date:x({formats:u,defaultWidth:"full"}),time:x({formats:d,defaultWidth:"full"}),dateTime:x({formats:l,defaultWidth:"full"})},hC=7,s=365.2425,r=Math.pow(10,8)*24*60*60*1000,fC=-r,_C=604800000,kC=86400000,gC=60000,yC=3600000,mC=1000,cC=525600,pC=43200,uC=1440,dC=60,lC=3,iC=12,sC=4,n=3600,rC=60,O=n*24,nC=O*7,o=O*s,a=o/12,oC=a*3,T=Symbol.for("constructDateFrom");function P(C,G){if(typeof C==="function")return C(G);if(C&&K(C)==="object"&&T in C)return C[T](G);if(C instanceof Date)return new C.constructor(G);return new Date(G)}function t(C){for(var G=arguments.length,J=new Array(G>1?G-1:0),X=1;X<G;X++)J[X-1]=arguments[X];var U=P.bind(null,C||J.find(function(Z){return K(Z)==="object"}));return J.map(U)}function e(){return z}function aC(C){z=C}var z={};function S(C,G){return P(G||C,C)}function w(C,G){var J,X,U,Z,B,H,q=e(),Y=(J=(X=(U=(Z=G===null||G===void 0?void 0:G.weekStartsOn)!==null&&Z!==void 0?Z:G===null||G===void 0||(B=G.locale)===null||B===void 0||(B=B.options)===null||B===void 0?void 0:B.weekStartsOn)!==null&&U!==void 0?U:q.weekStartsOn)!==null&&X!==void 0?X:(H=q.locale)===null||H===void 0||(H=H.options)===null||H===void 0?void 0:H.weekStartsOn)!==null&&J!==void 0?J:0,E=S(C,G===null||G===void 0?void 0:G.in),N=E.getDay(),FC=(N<Y?7:0)+N-Y;return E.setDate(E.getDate()-FC),E.setHours(0,0,0,0),E}function D(C,G,J){var X=t(J===null||J===void 0?void 0:J.in,C,G),U=h(X,2),Z=U[0],B=U[1];return+w(Z,J)===+w(B,J)}function CC(C){var G=$[C];switch(C){case 0:case 3:case 5:case 6:return"'\u0443 \u043C\u0438\u043D\u0443\u043B\u0443 "+G+" \u043E' p";case 1:case 2:case 4:return"'\u0443 \u043C\u0438\u043D\u0443\u043B\u0438\u0439 "+G+" \u043E' p"}}function L(C){var G=$[C];return"'\u0443 "+G+" \u043E' p"}function GC(C){var G=$[C];switch(C){case 0:case 3:case 5:case 6:return"'\u0443 \u043D\u0430\u0441\u0442\u0443\u043F\u043D\u0443 "+G+" \u043E' p";case 1:case 2:case 4:return"'\u0443 \u043D\u0430\u0441\u0442\u0443\u043F\u043D\u0438\u0439 "+G+" \u043E' p"}}var $=["\u043D\u0435\u0434\u0456\u043B\u044E","\u043F\u043E\u043D\u0435\u0434\u0456\u043B\u043E\u043A","\u0432\u0456\u0432\u0442\u043E\u0440\u043E\u043A","\u0441\u0435\u0440\u0435\u0434\u0443","\u0447\u0435\u0442\u0432\u0435\u0440","\u043F\u2019\u044F\u0442\u043D\u0438\u0446\u044E","\u0441\u0443\u0431\u043E\u0442\u0443"],JC=function C(G,J,X){var U=S(G),Z=U.getDay();if(D(U,J,X))return L(Z);else return CC(Z)},XC=function C(G,J,X){var U=S(G),Z=U.getDay();if(D(U,J,X))return L(Z);else return GC(Z)},ZC={lastWeek:JC,yesterday:"'\u0432\u0447\u043E\u0440\u0430 \u043E' p",today:"'\u0441\u044C\u043E\u0433\u043E\u0434\u043D\u0456 \u043E' p",tomorrow:"'\u0437\u0430\u0432\u0442\u0440\u0430 \u043E' p",nextWeek:XC,other:"P"},UC=function C(G,J,X,U){var Z=ZC[G];if(typeof Z==="function")return Z(J,X,U);return Z};function A(C){return function(G,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",U;if(X==="formatting"&&C.formattingValues){var Z=C.defaultFormattingWidth||C.defaultWidth,B=J!==null&&J!==void 0&&J.width?String(J.width):Z;U=C.formattingValues[B]||C.formattingValues[Z]}else{var H=C.defaultWidth,q=J!==null&&J!==void 0&&J.width?String(J.width):C.defaultWidth;U=C.values[q]||C.values[H]}var Y=C.argumentCallback?C.argumentCallback(G):G;return U[Y]}}var BC={narrow:["\u0434\u043E \u043D.\u0435.","\u043D.\u0435."],abbreviated:["\u0434\u043E \u043D. \u0435.","\u043D. \u0435."],wide:["\u0434\u043E \u043D\u0430\u0448\u043E\u0457 \u0435\u0440\u0438","\u043D\u0430\u0448\u043E\u0457 \u0435\u0440\u0438"]},HC={narrow:["1","2","3","4"],abbreviated:["1-\u0439 \u043A\u0432.","2-\u0439 \u043A\u0432.","3-\u0439 \u043A\u0432.","4-\u0439 \u043A\u0432."],wide:["1-\u0439 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","2-\u0439 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","3-\u0439 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","4-\u0439 \u043A\u0432\u0430\u0440\u0442\u0430\u043B"]},QC={narrow:["\u0421","\u041B","\u0411","\u041A","\u0422","\u0427","\u041B","\u0421","\u0412","\u0416","\u041B","\u0413"],abbreviated:["\u0441\u0456\u0447.","\u043B\u044E\u0442.","\u0431\u0435\u0440\u0435\u0437.","\u043A\u0432\u0456\u0442.","\u0442\u0440\u0430\u0432.","\u0447\u0435\u0440\u0432.","\u043B\u0438\u043F.","\u0441\u0435\u0440\u043F.","\u0432\u0435\u0440\u0435\u0441.","\u0436\u043E\u0432\u0442.","\u043B\u0438\u0441\u0442\u043E\u043F.","\u0433\u0440\u0443\u0434."],wide:["\u0441\u0456\u0447\u0435\u043D\u044C","\u043B\u044E\u0442\u0438\u0439","\u0431\u0435\u0440\u0435\u0437\u0435\u043D\u044C","\u043A\u0432\u0456\u0442\u0435\u043D\u044C","\u0442\u0440\u0430\u0432\u0435\u043D\u044C","\u0447\u0435\u0440\u0432\u0435\u043D\u044C","\u043B\u0438\u043F\u0435\u043D\u044C","\u0441\u0435\u0440\u043F\u0435\u043D\u044C","\u0432\u0435\u0440\u0435\u0441\u0435\u043D\u044C","\u0436\u043E\u0432\u0442\u0435\u043D\u044C","\u043B\u0438\u0441\u0442\u043E\u043F\u0430\u0434","\u0433\u0440\u0443\u0434\u0435\u043D\u044C"]},YC={narrow:["\u0421","\u041B","\u0411","\u041A","\u0422","\u0427","\u041B","\u0421","\u0412","\u0416","\u041B","\u0413"],abbreviated:["\u0441\u0456\u0447.","\u043B\u044E\u0442.","\u0431\u0435\u0440\u0435\u0437.","\u043A\u0432\u0456\u0442.","\u0442\u0440\u0430\u0432.","\u0447\u0435\u0440\u0432.","\u043B\u0438\u043F.","\u0441\u0435\u0440\u043F.","\u0432\u0435\u0440\u0435\u0441.","\u0436\u043E\u0432\u0442.","\u043B\u0438\u0441\u0442\u043E\u043F.","\u0433\u0440\u0443\u0434."],wide:["\u0441\u0456\u0447\u043D\u044F","\u043B\u044E\u0442\u043E\u0433\u043E","\u0431\u0435\u0440\u0435\u0437\u043D\u044F","\u043A\u0432\u0456\u0442\u043D\u044F","\u0442\u0440\u0430\u0432\u043D\u044F","\u0447\u0435\u0440\u0432\u043D\u044F","\u043B\u0438\u043F\u043D\u044F","\u0441\u0435\u0440\u043F\u043D\u044F","\u0432\u0435\u0440\u0435\u0441\u043D\u044F","\u0436\u043E\u0432\u0442\u043D\u044F","\u043B\u0438\u0441\u0442\u043E\u043F\u0430\u0434\u0430","\u0433\u0440\u0443\u0434\u043D\u044F"]},qC={narrow:["\u041D","\u041F","\u0412","\u0421","\u0427","\u041F","\u0421"],short:["\u043D\u0434","\u043F\u043D","\u0432\u0442","\u0441\u0440","\u0447\u0442","\u043F\u0442","\u0441\u0431"],abbreviated:["\u043D\u0435\u0434","\u043F\u043E\u043D","\u0432\u0456\u0432","\u0441\u0435\u0440","\u0447\u0442\u0432","\u043F\u0442\u043D","\u0441\u0443\u0431"],wide:["\u043D\u0435\u0434\u0456\u043B\u044F","\u043F\u043E\u043D\u0435\u0434\u0456\u043B\u043E\u043A","\u0432\u0456\u0432\u0442\u043E\u0440\u043E\u043A","\u0441\u0435\u0440\u0435\u0434\u0430","\u0447\u0435\u0442\u0432\u0435\u0440","\u043F\u2019\u044F\u0442\u043D\u0438\u0446\u044F","\u0441\u0443\u0431\u043E\u0442\u0430"]},EC={narrow:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u0456\u0432\u043D.",noon:"\u043F\u043E\u043B.",morning:"\u0440\u0430\u043D\u043E\u043A",afternoon:"\u0434\u0435\u043D\u044C",evening:"\u0432\u0435\u0447.",night:"\u043D\u0456\u0447"},abbreviated:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u0456\u0432\u043D.",noon:"\u043F\u043E\u043B.",morning:"\u0440\u0430\u043D\u043E\u043A",afternoon:"\u0434\u0435\u043D\u044C",evening:"\u0432\u0435\u0447.",night:"\u043D\u0456\u0447"},wide:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u0456\u0432\u043D\u0456\u0447",noon:"\u043F\u043E\u043B\u0443\u0434\u0435\u043D\u044C",morning:"\u0440\u0430\u043D\u043E\u043A",afternoon:"\u0434\u0435\u043D\u044C",evening:"\u0432\u0435\u0447\u0456\u0440",night:"\u043D\u0456\u0447"}},KC={narrow:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u0456\u0432\u043D.",noon:"\u043F\u043E\u043B.",morning:"\u0440\u0430\u043D\u043A\u0443",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u0456"},abbreviated:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u0456\u0432\u043D.",noon:"\u043F\u043E\u043B.",morning:"\u0440\u0430\u043D\u043A\u0443",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u0456"},wide:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u0456\u0432\u043D\u0456\u0447",noon:"\u043F\u043E\u043B\u0443\u0434\u0435\u043D\u044C",morning:"\u0440\u0430\u043D\u043A\u0443",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u0456"}},NC=function C(G,J){var X=String(J===null||J===void 0?void 0:J.unit),U=Number(G),Z;if(X==="date")if(U===3||U===23)Z="-\u0454";else Z="-\u0435";else if(X==="minute"||X==="second"||X==="hour")Z="-\u0430";else Z="-\u0439";return U+Z},VC={ordinalNumber:NC,era:A({values:BC,defaultWidth:"wide"}),quarter:A({values:HC,defaultWidth:"wide",argumentCallback:function C(G){return G-1}}),month:A({values:QC,defaultWidth:"wide",formattingValues:YC,defaultFormattingWidth:"wide"}),day:A({values:qC,defaultWidth:"wide"}),dayPeriod:A({values:EC,defaultWidth:"any",formattingValues:KC,defaultFormattingWidth:"wide"})};function I(C){return function(G){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,U=X&&C.matchPatterns[X]||C.matchPatterns[C.defaultMatchWidth],Z=G.match(U);if(!Z)return null;var B=Z[0],H=X&&C.parsePatterns[X]||C.parsePatterns[C.defaultParseWidth],q=Array.isArray(H)?IC(H,function(N){return N.test(B)}):AC(H,function(N){return N.test(B)}),Y;Y=C.valueCallback?C.valueCallback(q):q,Y=J.valueCallback?J.valueCallback(Y):Y;var E=G.slice(B.length);return{value:Y,rest:E}}}function AC(C,G){for(var J in C)if(Object.prototype.hasOwnProperty.call(C,J)&&G(C[J]))return J;return}function IC(C,G){for(var J=0;J<C.length;J++)if(G(C[J]))return J;return}function RC(C){return function(G){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=G.match(C.matchPattern);if(!X)return null;var U=X[0],Z=G.match(C.parsePattern);if(!Z)return null;var B=C.valueCallback?C.valueCallback(Z[0]):Z[0];B=J.valueCallback?J.valueCallback(B):B;var H=G.slice(U.length);return{value:B,rest:H}}}var MC=/^(\d+)(-?(е|й|є|а|я))?/i,xC=/\d+/i,SC={narrow:/^((до )?н\.?\s?е\.?)/i,abbreviated:/^((до )?н\.?\s?е\.?)/i,wide:/^(до нашої ери|нашої ери|наша ера)/i},$C={any:[/^д/i,/^н/i]},jC={narrow:/^[1234]/i,abbreviated:/^[1234](-?[иі]?й?)? кв.?/i,wide:/^[1234](-?[иі]?й?)? квартал/i},WC={any:[/1/i,/2/i,/3/i,/4/i]},OC={narrow:/^[слбктчвжг]/i,abbreviated:/^(січ|лют|бер(ез)?|квіт|трав|черв|лип|серп|вер(ес)?|жовт|лис(топ)?|груд)\.?/i,wide:/^(січень|січня|лютий|лютого|березень|березня|квітень|квітня|травень|травня|червня|червень|липень|липня|серпень|серпня|вересень|вересня|жовтень|жовтня|листопад[а]?|грудень|грудня)/i},TC={narrow:[/^с/i,/^л/i,/^б/i,/^к/i,/^т/i,/^ч/i,/^л/i,/^с/i,/^в/i,/^ж/i,/^л/i,/^г/i],any:[/^сі/i,/^лю/i,/^б/i,/^к/i,/^т/i,/^ч/i,/^лип/i,/^се/i,/^в/i,/^ж/i,/^лис/i,/^г/i]},PC={narrow:/^[нпвсч]/i,short:/^(нд|пн|вт|ср|чт|пт|сб)\.?/i,abbreviated:/^(нед|пон|вів|сер|че?тв|птн?|суб)\.?/i,wide:/^(неділ[яі]|понеділ[ок][ка]|вівтор[ок][ка]|серед[аи]|четвер(га)?|п\W*?ятниц[яі]|субот[аи])/i},zC={narrow:[/^н/i,/^п/i,/^в/i,/^с/i,/^ч/i,/^п/i,/^с/i],any:[/^н/i,/^п[он]/i,/^в/i,/^с[ер]/i,/^ч/i,/^п\W*?[ят]/i,/^с[уб]/i]},wC={narrow:/^([дп]п|півн\.?|пол\.?|ранок|ранку|день|дня|веч\.?|ніч|ночі)/i,abbreviated:/^([дп]п|півн\.?|пол\.?|ранок|ранку|день|дня|веч\.?|ніч|ночі)/i,wide:/^([дп]п|північ|полудень|ранок|ранку|день|дня|вечір|вечора|ніч|ночі)/i},DC={any:{am:/^дп/i,pm:/^пп/i,midnight:/^півн/i,noon:/^пол/i,morning:/^р/i,afternoon:/^д[ен]/i,evening:/^в/i,night:/^н/i}},LC={ordinalNumber:RC({matchPattern:MC,parsePattern:xC,valueCallback:function C(G){return parseInt(G,10)}}),era:I({matchPatterns:SC,defaultMatchWidth:"wide",parsePatterns:$C,defaultParseWidth:"any"}),quarter:I({matchPatterns:jC,defaultMatchWidth:"wide",parsePatterns:WC,defaultParseWidth:"any",valueCallback:function C(G){return G+1}}),month:I({matchPatterns:OC,defaultMatchWidth:"wide",parsePatterns:TC,defaultParseWidth:"any"}),day:I({matchPatterns:PC,defaultMatchWidth:"wide",parsePatterns:zC,defaultParseWidth:"any"}),dayPeriod:I({matchPatterns:wC,defaultMatchWidth:"wide",parsePatterns:DC,defaultParseWidth:"any"})},vC={code:"uk",formatDistance:p,formatLong:i,formatRelative:UC,localize:VC,match:LC,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=R(R({},window.dateFns),{},{locale:R(R({},(M=window.dateFns)===null||M===void 0?void 0:M.locale),{},{uk:vC})})})();

//# debugId=1F1B83DEDC0F349F64756E2164756E21
