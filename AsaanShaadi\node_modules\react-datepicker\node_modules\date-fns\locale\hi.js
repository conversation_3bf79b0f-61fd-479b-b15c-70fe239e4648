import { formatDistance } from "./hi/_lib/formatDistance.js";
import { formatLong } from "./hi/_lib/formatLong.js";
import { formatRelative } from "./hi/_lib/formatRelative.js";
import { localize } from "./hi/_lib/localize.js";
import { match } from "./hi/_lib/match.js";

/**
 * @category Locales
 * @summary Hindi locale (India).
 * @language Hindi
 * @iso-639-2 hin
 * <AUTHOR> [@mukeshmandiwal](https://github.com/mukeshmandiwal)
 */
export const hi = {
  code: "hi",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default hi;
