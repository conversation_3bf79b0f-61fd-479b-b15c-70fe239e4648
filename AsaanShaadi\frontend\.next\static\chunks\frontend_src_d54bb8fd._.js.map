{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Shaadi%20app/AsaanShaadi/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Shaadi%20app/AsaanShaadi/frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { useAuthStore } from \"@/store/authStore\";\nimport { Loader2 } from \"lucide-react\";\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredRole?: string[];\n  redirectTo?: string;\n}\n\nexport function ProtectedRoute({ \n  children, \n  requiredRole = [], \n  redirectTo = \"/auth/login\" \n}: ProtectedRouteProps) {\n  const router = useRouter();\n  const { user, isAuthenticated, isLoading } = useAuthStore();\n\n  useEffect(() => {\n    // Wait for auth state to be loaded from localStorage\n    if (isLoading) return;\n\n    // If not authenticated, redirect to login\n    if (!isAuthenticated || !user) {\n      router.push(redirectTo);\n      return;\n    }\n\n    // If specific roles are required, check user role\n    if (requiredRole.length > 0 && !requiredRole.includes(user.role)) {\n      // Redirect based on user role\n      if (user.role === 'ADMIN') {\n        router.push('/admin');\n      } else if (user.role === 'VENDOR') {\n        router.push('/vendor');\n      } else {\n        router.push('/');\n      }\n      return;\n    }\n  }, [isAuthenticated, user, isLoading, router, requiredRole, redirectTo]);\n\n  // Show loading while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Loader2 className=\"h-8 w-8 animate-spin mx-auto mb-4\" />\n          <p className=\"text-muted-foreground\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Don't render children if not authenticated or wrong role\n  if (!isAuthenticated || !user) {\n    return null;\n  }\n\n  if (requiredRole.length > 0 && !requiredRole.includes(user.role)) {\n    return null;\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAaO,SAAS,eAAe,EAC7B,QAAQ,EACR,eAAe,EAAE,EACjB,aAAa,aAAa,EACN;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAExD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,qDAAqD;YACrD,IAAI,WAAW;YAEf,0CAA0C;YAC1C,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,kDAAkD;YAClD,IAAI,aAAa,MAAM,GAAG,KAAK,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;gBAChE,8BAA8B;gBAC9B,IAAI,KAAK,IAAI,KAAK,SAAS;oBACzB,OAAO,IAAI,CAAC;gBACd,OAAO,IAAI,KAAK,IAAI,KAAK,UAAU;oBACjC,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;gBACA;YACF;QACF;mCAAG;QAAC;QAAiB;QAAM;QAAW;QAAQ;QAAc;KAAW;IAEvE,6CAA6C;IAC7C,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,2DAA2D;IAC3D,IAAI,CAAC,mBAAmB,CAAC,MAAM;QAC7B,OAAO;IACT;IAEA,IAAI,aAAa,MAAM,GAAG,KAAK,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QAChE,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GAtDgB;;QAKC,qIAAA,CAAA,YAAS;QACqB,wIAAA,CAAA,eAAY;;;KAN3C", "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Shaadi%20app/AsaanShaadi/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Shaadi%20app/AsaanShaadi/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Shaadi%20app/Asaan<PERSON>haadi/frontend/src/app/admin/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from \"@/components/ui/card\";\nimport { ProtectedRoute } from \"@/components/auth/ProtectedRoute\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from \"@/components/ui/tabs\";\nimport { \n  Users, \n  MapPin, \n  ChefHat, \n  Calendar, \n  DollarSign, \n  TrendingUp,\n  Eye,\n  Edit,\n  Trash2,\n  Plus\n} from \"lucide-react\";\n\n// Mock data for admin dashboard\nconst stats = {\n  totalUsers: 1247,\n  totalVenues: 89,\n  totalCaterers: 156,\n  totalBookings: 342,\n  monthlyRevenue: 2450000,\n  pendingApprovals: 12,\n};\n\nconst recentBookings = [\n  {\n    id: \"1\",\n    customerName: \"Ahmed Khan\",\n    venueName: \"Royal Palace Banquet\",\n    eventDate: \"2024-08-15\",\n    status: \"confirmed\",\n    amount: 150000,\n  },\n  {\n    id: \"2\",\n    customerName: \"<PERSON><PERSON>\",\n    venueName: \"Garden View Hall\",\n    eventDate: \"2024-07-20\",\n    status: \"pending\",\n    amount: 80000,\n  },\n  {\n    id: \"3\",\n    customerName: \"<PERSON>\",\n    venueName: \"Crystal Ballroom\",\n    eventDate: \"2024-09-10\",\n    status: \"confirmed\",\n    amount: 250000,\n  },\n];\n\nconst pendingVenues = [\n  {\n    id: \"1\",\n    name: \"Elegant Gardens\",\n    owner: \"Muhammad Tariq\",\n    location: \"Lahore\",\n    submittedDate: \"2024-06-20\",\n  },\n  {\n    id: \"2\",\n    name: \"Grand Marquee\",\n    owner: \"Ali Hassan\",\n    location: \"Islamabad\",\n    submittedDate: \"2024-06-18\",\n  },\n];\n\nexport default function AdminDashboard() {\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case \"confirmed\":\n        return \"bg-green-100 text-green-800\";\n      case \"pending\":\n        return \"bg-yellow-100 text-yellow-800\";\n      case \"cancelled\":\n        return \"bg-red-100 text-red-800\";\n      default:\n        return \"bg-gray-100 text-gray-800\";\n    }\n  };\n\n  return (\n    <ProtectedRoute requiredRole={['ADMIN']}>\n      <div className=\"container mx-auto px-4 py-8\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl lg:text-4xl font-bold mb-4\">Admin Dashboard</h1>\n        <p className=\"text-xl text-muted-foreground\">\n          Manage your platform and monitor business metrics\n        </p>\n      </div>\n\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Users</CardTitle>\n            <Users className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{stats.totalUsers.toLocaleString()}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              +12% from last month\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Venues</CardTitle>\n            <MapPin className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{stats.totalVenues}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              +3 new this month\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Caterers</CardTitle>\n            <ChefHat className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{stats.totalCaterers}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              +8 new this month\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Bookings</CardTitle>\n            <Calendar className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{stats.totalBookings}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              +23% from last month\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Revenue</CardTitle>\n            <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">₹{(stats.monthlyRevenue / 1000000).toFixed(1)}M</div>\n            <p className=\"text-xs text-muted-foreground\">\n              +18% from last month\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Pending</CardTitle>\n            <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{stats.pendingApprovals}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Require approval\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      <Tabs defaultValue=\"bookings\" className=\"space-y-6\">\n        <TabsList className=\"grid w-full grid-cols-4\">\n          <TabsTrigger value=\"bookings\">Recent Bookings</TabsTrigger>\n          <TabsTrigger value=\"venues\">Venue Management</TabsTrigger>\n          <TabsTrigger value=\"caterers\">Caterer Management</TabsTrigger>\n          <TabsTrigger value=\"approvals\">Pending Approvals</TabsTrigger>\n        </TabsList>\n\n        {/* Recent Bookings */}\n        <TabsContent value=\"bookings\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Recent Bookings</CardTitle>\n              <CardDescription>\n                Latest booking requests and confirmations\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {recentBookings.map((booking) => (\n                  <div key={booking.id} className=\"flex items-center justify-between p-4 border rounded-lg\">\n                    <div className=\"space-y-1\">\n                      <p className=\"font-medium\">{booking.customerName}</p>\n                      <p className=\"text-sm text-muted-foreground\">{booking.venueName}</p>\n                      <p className=\"text-sm text-muted-foreground\">\n                        Event Date: {new Date(booking.eventDate).toLocaleDateString()}\n                      </p>\n                    </div>\n                    <div className=\"flex items-center gap-4\">\n                      <div className=\"text-right\">\n                        <p className=\"font-medium\">₹{booking.amount.toLocaleString()}</p>\n                        <Badge className={getStatusColor(booking.status)}>\n                          {booking.status}\n                        </Badge>\n                      </div>\n                      <div className=\"flex gap-2\">\n                        <Button size=\"sm\" variant=\"outline\">\n                          <Eye className=\"h-4 w-4\" />\n                        </Button>\n                        <Button size=\"sm\" variant=\"outline\">\n                          <Edit className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        {/* Venue Management */}\n        <TabsContent value=\"venues\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between\">\n              <div>\n                <CardTitle>Venue Management</CardTitle>\n                <CardDescription>\n                  Manage all venues on the platform\n                </CardDescription>\n              </div>\n              <Button>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Add Venue\n              </Button>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-center py-12\">\n                <MapPin className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold mb-2\">Venue Management</h3>\n                <p className=\"text-muted-foreground\">\n                  Venue management interface will be implemented here\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        {/* Caterer Management */}\n        <TabsContent value=\"caterers\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between\">\n              <div>\n                <CardTitle>Caterer Management</CardTitle>\n                <CardDescription>\n                  Manage all caterers on the platform\n                </CardDescription>\n              </div>\n              <Button>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Add Caterer\n              </Button>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-center py-12\">\n                <ChefHat className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold mb-2\">Caterer Management</h3>\n                <p className=\"text-muted-foreground\">\n                  Caterer management interface will be implemented here\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        {/* Pending Approvals */}\n        <TabsContent value=\"approvals\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Pending Approvals</CardTitle>\n              <CardDescription>\n                Review and approve new venue and caterer applications\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {pendingVenues.map((venue) => (\n                  <div key={venue.id} className=\"flex items-center justify-between p-4 border rounded-lg\">\n                    <div className=\"space-y-1\">\n                      <p className=\"font-medium\">{venue.name}</p>\n                      <p className=\"text-sm text-muted-foreground\">Owner: {venue.owner}</p>\n                      <p className=\"text-sm text-muted-foreground\">Location: {venue.location}</p>\n                      <p className=\"text-sm text-muted-foreground\">\n                        Submitted: {new Date(venue.submittedDate).toLocaleDateString()}\n                      </p>\n                    </div>\n                    <div className=\"flex gap-2\">\n                      <Button size=\"sm\" variant=\"outline\">\n                        <Eye className=\"h-4 w-4 mr-2\" />\n                        Review\n                      </Button>\n                      <Button size=\"sm\">\n                        Approve\n                      </Button>\n                      <Button size=\"sm\" variant=\"destructive\">\n                        <Trash2 className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n      </div>\n    </ProtectedRoute>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAoBA,gCAAgC;AAChC,MAAM,QAAQ;IACZ,YAAY;IACZ,aAAa;IACb,eAAe;IACf,eAAe;IACf,gBAAgB;IAChB,kBAAkB;AACpB;AAEA,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,WAAW;QACX,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,WAAW;QACX,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,WAAW;QACX,QAAQ;QACR,QAAQ;IACV;CACD;AAED,MAAM,gBAAgB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,eAAe;IACjB;CACD;AAEc,SAAS;IACtB,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,2JAAA,CAAA,iBAAc;QAAC,cAAc;YAAC;SAAQ;kBACrC,cAAA,6LAAC;YAAI,WAAU;;8BACf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,6LAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;8BAM/C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+IAAA,CAAA,OAAI;;8CACH,6LAAC,+IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,+IAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,6LAAC,+IAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDAAsB,MAAM,UAAU,CAAC,cAAc;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,6LAAC,+IAAA,CAAA,OAAI;;8CACH,6LAAC,+IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,+IAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;8CAEpB,6LAAC,+IAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDAAsB,MAAM,WAAW;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,6LAAC,+IAAA,CAAA,OAAI;;8CACH,6LAAC,+IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,+IAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;8CAErB,6LAAC,+IAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDAAsB,MAAM,aAAa;;;;;;sDACxD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,6LAAC,+IAAA,CAAA,OAAI;;8CACH,6LAAC,+IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,+IAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,6LAAC,+IAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDAAsB,MAAM,aAAa;;;;;;sDACxD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,6LAAC,+IAAA,CAAA,OAAI;;8CACH,6LAAC,+IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,+IAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,6LAAC,+IAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;;gDAAqB;gDAAE,CAAC,MAAM,cAAc,GAAG,OAAO,EAAE,OAAO,CAAC;gDAAG;;;;;;;sDAClF,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,6LAAC,+IAAA,CAAA,OAAI;;8CACH,6LAAC,+IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,+IAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,6LAAC,+IAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDAAsB,MAAM,gBAAgB;;;;;;sDAC3D,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;8BAOnD,6LAAC,+IAAA,CAAA,OAAI;oBAAC,cAAa;oBAAW,WAAU;;sCACtC,6LAAC,+IAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6LAAC,+IAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,6LAAC,+IAAA,CAAA,cAAW;oCAAC,OAAM;8CAAS;;;;;;8CAC5B,6LAAC,+IAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,6LAAC,+IAAA,CAAA,cAAW;oCAAC,OAAM;8CAAY;;;;;;;;;;;;sCAIjC,6LAAC,+IAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,6LAAC,+IAAA,CAAA,OAAI;;kDACH,6LAAC,+IAAA,CAAA,aAAU;;0DACT,6LAAC,+IAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,+IAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,+IAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,6LAAC;oDAAqB,WAAU;;sEAC9B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAAe,QAAQ,YAAY;;;;;;8EAChD,6LAAC;oEAAE,WAAU;8EAAiC,QAAQ,SAAS;;;;;;8EAC/D,6LAAC;oEAAE,WAAU;;wEAAgC;wEAC9B,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;sEAG/D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;;gFAAc;gFAAE,QAAQ,MAAM,CAAC,cAAc;;;;;;;sFAC1D,6LAAC,gJAAA,CAAA,QAAK;4EAAC,WAAW,eAAe,QAAQ,MAAM;sFAC5C,QAAQ,MAAM;;;;;;;;;;;;8EAGnB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,iJAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAQ;sFACxB,cAAA,6LAAC,mMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;;;;;;sFAEjB,6LAAC,iJAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAQ;sFACxB,cAAA,6LAAC,8MAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;mDApBd,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;sCAgC9B,6LAAC,+IAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,6LAAC,+IAAA,CAAA,OAAI;;kDACH,6LAAC,+IAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC;;kEACC,6LAAC,+IAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,+IAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,6LAAC,iJAAA,CAAA,SAAM;;kEACL,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAIrC,6LAAC,+IAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS7C,6LAAC,+IAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,6LAAC,+IAAA,CAAA,OAAI;;kDACH,6LAAC,+IAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC;;kEACC,6LAAC,+IAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,+IAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,6LAAC,iJAAA,CAAA,SAAM;;kEACL,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAIrC,6LAAC,+IAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS7C,6LAAC,+IAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,6LAAC,+IAAA,CAAA,OAAI;;kDACH,6LAAC,+IAAA,CAAA,aAAU;;0DACT,6LAAC,+IAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,+IAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,+IAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,6LAAC;oDAAmB,WAAU;;sEAC5B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAAe,MAAM,IAAI;;;;;;8EACtC,6LAAC;oEAAE,WAAU;;wEAAgC;wEAAQ,MAAM,KAAK;;;;;;;8EAChE,6LAAC;oEAAE,WAAU;;wEAAgC;wEAAW,MAAM,QAAQ;;;;;;;8EACtE,6LAAC;oEAAE,WAAU;;wEAAgC;wEAC/B,IAAI,KAAK,MAAM,aAAa,EAAE,kBAAkB;;;;;;;;;;;;;sEAGhE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,iJAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;;sFACxB,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAGlC,6LAAC,iJAAA,CAAA,SAAM;oEAAC,MAAK;8EAAK;;;;;;8EAGlB,6LAAC,iJAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;8EACxB,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;mDAlBd,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BpC;KA7PwB", "debugId": null}}]}