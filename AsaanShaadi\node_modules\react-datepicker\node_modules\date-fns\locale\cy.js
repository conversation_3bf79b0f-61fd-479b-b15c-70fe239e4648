import { formatDistance } from "./cy/_lib/formatDistance.js";
import { formatLong } from "./cy/_lib/formatLong.js";
import { formatRelative } from "./cy/_lib/formatRelative.js";
import { localize } from "./cy/_lib/localize.js";
import { match } from "./cy/_lib/match.js";

/**
 * @category Locales
 * @summary Welsh locale.
 * @language Welsh
 * @iso-639-2 cym
 * <AUTHOR> [@elmomalmo](https://github.com/elmomalmo)
 */
export const cy = {
  code: "cy",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default cy;
